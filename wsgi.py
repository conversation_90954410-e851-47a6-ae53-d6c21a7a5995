#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WSGI配置文件
用于在生产环境中部署海上平台工具官网
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入应用
from app import application

# 确保这是WSGI入口点
if __name__ == "__main__":
    # 如果直接运行，启动开发服务器
    from wsgiref.simple_server import make_server
    
    print("🚀 启动WSGI开发服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("⚓ 海上平台常用查询工具官网")
    
    server = make_server('localhost', 8000, application)
    server.serve_forever()
