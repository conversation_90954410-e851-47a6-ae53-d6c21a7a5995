#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海上平台常用查询工具 - 官网
简单的WSGI应用，用于展示小程序介绍页面
"""

import os
import mimetypes
from urllib.parse import unquote

def application(environ, start_response):
    """WSGI应用主函数"""
    
    # 获取请求路径
    path = environ.get('PATH_INFO', '/')
    method = environ.get('REQUEST_METHOD', 'GET')
    
    # 只处理GET请求
    if method != 'GET':
        status = '405 Method Not Allowed'
        headers = [('Content-Type', 'text/plain; charset=utf-8')]
        start_response(status, headers)
        return [b'Method Not Allowed']
    
    # 处理根路径，返回主页
    if path == '/' or path == '/index.html':
        return serve_index_page(start_response)
    
    # 处理静态文件请求
    if path.startswith('/static/') or path.startswith('/images/'):
        return serve_static_file(path, start_response)
    
    # 处理favicon请求
    if path == '/favicon.ico':
        status = '200 OK'
        headers = [('Content-Type', 'image/x-icon')]
        start_response(status, headers)
        # 返回一个简单的favicon
        return [b'']
    
    # 404处理
    return serve_404(start_response)

def serve_index_page(start_response):
    """服务主页"""
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(current_dir, 'index.html')
        
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        status = '200 OK'
        headers = [
            ('Content-Type', 'text/html; charset=utf-8'),
            ('Cache-Control', 'no-cache, no-store, must-revalidate'),
            ('Pragma', 'no-cache'),
            ('Expires', '0')
        ]
        start_response(status, headers)
        return [content.encode('utf-8')]
        
    except FileNotFoundError:
        return serve_404(start_response)
    except Exception as e:
        return serve_error(start_response, str(e))


def serve_static_file(path, start_response):
    """服务静态文件"""
    try:
        # 安全检查，防止路径遍历攻击
        if '..' in path:
            return serve_404(start_response)

        current_dir = os.path.dirname(os.path.abspath(__file__))
        file_path = os.path.join(current_dir, path.lstrip('/'))

        # 检查文件是否存在
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            return serve_404(start_response)
        
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'
        
        with open(file_path, 'rb') as f:
            content = f.read()
        
        status = '200 OK'
        headers = [
            ('Content-Type', mime_type),
            ('Content-Length', str(len(content))),
            ('Cache-Control', 'public, max-age=3600')
        ]
        start_response(status, headers)
        return [content]
        
    except Exception as e:
        return serve_error(start_response, str(e))

def serve_404(start_response):
    """404页面"""
    content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>页面未找到 - 海上平台工具</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                color: white;
            }
            .error-container {
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                padding: 60px 40px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            }
            .error-code {
                font-size: 72px;
                font-weight: 700;
                margin-bottom: 20px;
            }
            .error-message {
                font-size: 24px;
                margin-bottom: 30px;
                opacity: 0.9;
            }
            .back-link {
                display: inline-block;
                padding: 12px 24px;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                border-radius: 8px;
                transition: background 0.3s ease;
            }
            .back-link:hover {
                background: rgba(255, 255, 255, 0.3);
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-code">404</div>
            <div class="error-message">页面未找到</div>
            <a href="/" class="back-link">返回首页</a>
        </div>
    </body>
    </html>
    """
    
    status = '404 Not Found'
    headers = [('Content-Type', 'text/html; charset=utf-8')]
    start_response(status, headers)
    return [content.encode('utf-8')]

def serve_error(start_response, error_msg):
    """错误页面"""
    content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>服务器错误 - 海上平台工具</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0;
                color: white;
            }}
            .error-container {{
                text-align: center;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                border-radius: 20px;
                padding: 60px 40px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                max-width: 500px;
            }}
            .error-code {{
                font-size: 72px;
                font-weight: 700;
                margin-bottom: 20px;
            }}
            .error-message {{
                font-size: 18px;
                margin-bottom: 30px;
                opacity: 0.9;
                line-height: 1.6;
            }}
            .back-link {{
                display: inline-block;
                padding: 12px 24px;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                border-radius: 8px;
                transition: background 0.3s ease;
            }}
            .back-link:hover {{
                background: rgba(255, 255, 255, 0.3);
            }}
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-code">500</div>
            <div class="error-message">
                服务器内部错误<br>
                <small style="opacity: 0.7;">{error_msg}</small>
            </div>
            <a href="/" class="back-link">返回首页</a>
        </div>
    </body>
    </html>
    """
    
    status = '500 Internal Server Error'
    headers = [('Content-Type', 'text/html; charset=utf-8')]
    start_response(status, headers)
    return [content.encode('utf-8')]

# 如果直接运行此文件，启动开发服务器
if __name__ == '__main__':
    from wsgiref.simple_server import make_server
    
    print("🚀 启动开发服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("⚓ 海上平台常用查询工具官网")
    print("按 Ctrl+C 停止服务器")
    
    server = make_server('localhost', 8000, application)
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
