<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海上平台常用查询工具 - 微信小程序</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚓</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 15px 0;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            text-decoration: none;
        }

        .logo::before {
            content: "⚓";
            margin-right: 8px;
        }

        /* 主要内容 */
        .main-content {
            margin-top: 80px;
            padding: 30px 0;
        }

        .hero-section {
            text-align: center;
            padding: 40px 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .hero-title {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 20px;
            color: #86868b;
            margin-bottom: 25px;
            font-weight: 400;
        }

        .hero-description {
            font-size: 16px;
            color: #515154;
            max-width: 600px;
            margin: 0 auto 30px;
            line-height: 1.6;
        }

        /* 功能介绍 */
        .features-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 40px;
            margin-bottom: 60px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 36px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 50px;
            color: #1d1d1f;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: rgba(247, 247, 247, 0.8);
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1d1d1f;
        }

        .feature-description {
            font-size: 16px;
            color: #515154;
            line-height: 1.6;
        }

        /* 二维码部分 */
        .qr-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 40px;
            margin-bottom: 60px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .qr-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 60px;
            flex-wrap: wrap;
        }

        .qr-content {
            flex: 1;
            min-width: 300px;
        }

        .qr-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1d1d1f;
        }

        .qr-description {
            font-size: 18px;
            color: #515154;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            background: #f5f5f7;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 80px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .qr-code:hover {
            transform: scale(1.05);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .qr-hover-tip {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            white-space: nowrap;
        }

        .qr-code:hover .qr-hover-tip {
            opacity: 1;
        }

        .download-tips {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
            border-left: 4px solid #007aff;
        }

        .download-tips p {
            font-size: 16px;
            color: #007aff;
            margin: 5px 0;
        }

        /* 底部 */
        .footer {
            text-align: center;
            padding: 40px 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
        }

        .footer a:hover {
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-section {
                padding: 30px 0;
                margin-bottom: 30px;
            }

            .hero-title {
                font-size: 28px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .hero-description {
                font-size: 15px;
                margin-bottom: 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .qr-container {
                flex-direction: column;
                gap: 40px;
            }

            .container {
                padding: 0 15px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-section,
        .features-section,
        .qr-section,
        .extended-features-section {
            animation: fadeInUp 0.8s ease-out;
        }

        .features-section {
            animation-delay: 0.2s;
        }

        .qr-section {
            animation-delay: 0.4s;
        }

        .extended-features-section {
            animation-delay: 0.6s;
        }

        /* 二维码弹窗样式 */
        .qr-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            z-index: 2000;
            animation: fadeIn 0.3s ease;
        }

        .qr-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-modal-content {
            background: white;
            border-radius: 20px;
            padding: 0;
            max-width: 400px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
        }

        .qr-modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .qr-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .qr-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: white;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .qr-modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .qr-modal-body {
            padding: 40px 30px;
            text-align: center;
        }

        .qr-modal-image {
            width: 250px;
            height: 250px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .qr-modal-tip {
            color: #666;
            font-size: 16px;
            margin: 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* 扩展功能推荐样式 */
        .extended-features-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 60px 40px;
            margin-bottom: 60px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .extended-features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 50px;
            margin-top: 40px;
        }

        .extended-feature-card {
            background: rgba(247, 247, 247, 0.8);
            border-radius: 20px;
            padding: 40px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .extended-feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .support-card {
            background: linear-gradient(135deg, rgba(255, 182, 193, 0.2) 0%, rgba(255, 160, 122, 0.2) 100%);
        }

        .support-card:hover {
            border-color: rgba(255, 105, 180, 0.4);
        }

        .extended-feature-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .extended-feature-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1d1d1f;
        }

        .extended-feature-description {
            font-size: 16px;
            color: #515154;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .extended-feature-highlights {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 25px;
        }

        .highlight-tag {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .support-tag {
            background: rgba(255, 105, 180, 0.1);
            color: #ff69b4;
        }

        .extended-feature-btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .extended-feature-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .support-btn {
            background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
        }

        .support-btn:hover {
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .qr-modal-content {
                width: 95%;
                margin: 20px;
            }

            .qr-modal-image {
                width: 200px;
                height: 200px;
            }

            .qr-modal-header {
                padding: 15px 20px;
            }

            .qr-modal-body {
                padding: 30px 20px;
            }

            .extended-features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .extended-feature-card {
                padding: 30px 25px;
            }

            .extended-features-section {
                padding: 40px 25px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="logo">海上平台常用查询工具</a>
                <div style="font-size: 14px; color: #86868b;">
                    专业 · 便捷 · 高效
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <!-- 英雄区域 -->
            <section class="hero-section">
                <h1 class="hero-title">海上平台常用查询工具</h1>
                <p class="hero-subtitle">Offshore Platform Common Query Tools</p>
                <p class="hero-description">
                    专为海上平台工作人员打造的专业工具集，涵盖含油化验计算、倒班查询、
                    药剂标定统计、设备查询等核心功能，让海上作业更加专业高效。
                </p>
            </section>

            <!-- 功能介绍 -->
            <section class="features-section">
                <h2 class="section-title">核心功能</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">🧪</span>
                        <h3 class="feature-title">含油化验计算</h3>
                        <p class="feature-description">
                            专业的含油化验相关计算工具，
                            快速准确计算各项化验参数，提高工作效率。
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⏰</span>
                        <h3 class="feature-title">倒班时间查询</h3>
                        <p class="feature-description">
                            智能倒班时间查询系统，
                            轻松掌握班次安排，合理规划工作和休息。
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📊</span>
                        <h3 class="feature-title">药剂标定统计</h3>
                        <p class="feature-description">
                            药剂标定数据统计分析，
                            精确记录和分析标定结果，确保数据准确性。
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">🔍</span>
                        <h3 class="feature-title">仪表位号查询</h3>
                        <p class="feature-description">
                            快速查询仪表及位号信息，
                            便捷的设备信息检索，提升维护效率。
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">⚠️</span>
                        <h3 class="feature-title">防爆标识查询</h3>
                        <p class="feature-description">
                            防爆标识快速查询识别，
                            确保安全作业，规范防爆设备使用。
                        </p>
                    </div>
                    <div class="feature-card">
                        <span class="feature-icon">📸</span>
                        <h3 class="feature-title">证件照背景更换</h3>
                        <p class="feature-description">
                            便捷的证件照背景更换工具，
                            一键更换背景色，满足各种证件照需求。
                        </p>
                    </div>
                </div>
            </section>

            <!-- 二维码部分 -->
            <section class="qr-section">
                <div class="qr-container">
                    <div class="qr-content">
                        <h2 class="qr-title">立即体验</h2>
                        <p class="qr-description">
                            使用微信扫描右侧二维码，即可体验含油化验计算、倒班查询、药剂标定等专业功能。
                        </p>
                        <div class="download-tips">
                            <p><strong>使用说明：</strong></p>
                            <p>1. 使用微信扫描二维码</p>
                            <p>2. 点击进入小程序</p>
                            <p>3. 微信授权登录开始使用</p>
                            <p>4. 享受专业的海上平台常用查询工具服务</p>
                        </div>
                    </div>
                    <div class="qr-code" onclick="openQRModal()">
                        <img src="images/qr-code.png" alt="海上平台常用查询工具小程序二维码"
                             style="width: 100%; height: 100%; border-radius: 16px; object-fit: cover; cursor: pointer;">
                        <div class="qr-hover-tip">点击放大</div>
                    </div>
                </div>
            </section>

            <!-- 扩展功能推荐 -->
            <section class="extended-features-section">
                <h2 class="section-title">更多专业工具</h2>

                <div class="extended-features-grid">
                    <!-- 液量核算系统 -->
                    <div class="extended-feature-card">
                        <div class="extended-feature-icon">🛢️</div>
                        <div class="extended-feature-content">
                            <h3 class="extended-feature-title">液量核算系统</h3>
                            <p class="extended-feature-description">
                                专业的油井液量核算管理系统，支持Excel数据导入、停开井状态管理、
                                产量影响计算、支线统计分析等功能，为海上平台液量核算提供全面解决方案。
                            </p>
                            <div class="extended-feature-highlights">
                                <span class="highlight-tag">数据导入</span>
                                <span class="highlight-tag">状态管理</span>
                                <span class="highlight-tag">产量核算</span>
                                <span class="highlight-tag">报表生成</span>
                            </div>
                            <a href="https://tj.sunxiyue.com/" target="_blank" class="extended-feature-btn">
                                立即体验 →
                            </a>
                        </div>
                    </div>

                    <!-- 友情赞助 -->
                    <div class="extended-feature-card support-card">
                        <div class="extended-feature-icon">❤️</div>
                        <div class="extended-feature-content">
                            <h3 class="extended-feature-title">支持我们的工作</h3>
                            <p class="extended-feature-description">
                                如果我们的工具对您的工作有所帮助，欢迎通过友情赞助支持我们继续开发更多实用工具。
                                您的每一份支持都是我们前进的动力，让我们能为海上平台工作者提供更好的服务。
                            </p>
                            <div class="extended-feature-highlights">
                                <span class="highlight-tag support-tag">自愿赞助</span>
                                <span class="highlight-tag support-tag">持续开发</span>
                                <span class="highlight-tag support-tag">服务改进</span>
                            </div>
                            <a href="https://sunxiyue.com/2/" target="_blank" class="extended-feature-btn support-btn">
                                友情赞助 💝
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 二维码放大弹窗 -->
    <div id="qrModal" class="qr-modal" onclick="closeQRModal()">
        <div class="qr-modal-content" onclick="event.stopPropagation()">
            <div class="qr-modal-header">
                <h3>扫描二维码体验小程序</h3>
                <button class="qr-modal-close" onclick="closeQRModal()">&times;</button>
            </div>
            <div class="qr-modal-body">
                <img src="images/qr-code.png" alt="海上平台常用查询工具小程序二维码" class="qr-modal-image">
                <p class="qr-modal-tip">使用微信扫描上方二维码</p>
            </div>
        </div>
    </div>

    <!-- 底部 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 海上平台常用查询工具. All rights reserved.</p>
            <p>联系我们: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p>官网: <a href="https://wx.sunxiyue.com" target="_blank">wx.sunxiyue.com</a> |
               <a href="https://zdh.sunxiyue.com/" target="_blank" style="font-size: 12px; opacity: 0.6;">管理</a></p>
        </div>
    </footer>

    <script>
        // 简单的滚动动画
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 二维码弹窗功能
        function openQRModal() {
            const modal = document.getElementById('qrModal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        }

        function closeQRModal() {
            const modal = document.getElementById('qrModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);

            // 恢复背景滚动
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeQRModal();
            }
        });
    </script>
</body>
</html>
