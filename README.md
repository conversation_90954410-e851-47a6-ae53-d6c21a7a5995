# 海上平台常用查询工具 - 官网

这是一个Apple风格的高级网页，用于介绍海上平台常用查询工具微信小程序。

## 🎨 设计特色

- **Apple风格设计**：采用苹果公司的设计语言，简洁优雅
- **毛玻璃效果**：使用backdrop-filter实现现代化的毛玻璃背景
- **响应式布局**：完美适配桌面端和移动端
- **流畅动画**：CSS3动画效果，提升用户体验
- **渐变背景**：美观的渐变色背景设计

## 📁 文件结构

```
wd/
├── index.html      # 主页面HTML文件
├── app.py          # Python WSGI应用
├── wsgi.py         # WSGI配置文件
├── README.md       # 说明文档
└── images/         # 图片资源文件夹
    ├── qr-code.png # 小程序二维码图片
    └── README.md   # 图片说明文档
```

## 🚀 部署方式

### 方式一：直接访问HTML文件
将 `index.html` 文件上传到服务器的web目录，直接通过浏览器访问。

### 方式二：使用Python WSGI应用

1. **上传文件**：将所有文件上传到服务器的 `wd` 目录

2. **配置Web服务器**：
   - 如果使用Apache，确保mod_wsgi已启用
   - 如果使用Nginx，配置uwsgi或gunicorn

3. **设置WSGI入口**：
   ```python
   # 在你的主wsgi.py文件中添加路由
   from wd.app import application as miniprogram_site
   
   def application(environ, start_response):
       path = environ.get('PATH_INFO', '/')
       if path.startswith('/miniprogram/'):
           # 修改路径，去掉前缀
           environ['PATH_INFO'] = path[12:]  # 去掉 '/miniprogram'
           return miniprogram_site(environ, start_response)
       else:
           # 其他路径的处理...
           pass
   ```

4. **访问地址**：
   - 主站：`https://sunxiyue.com/miniprogram/`
   - 或者配置子域名：`https://miniprogram.sunxiyue.com/`

## 🔧 本地开发

```bash
# 进入项目目录
cd wd

# 运行开发服务器
python app.py

# 或者使用wsgi.py
python wsgi.py

# 访问 http://localhost:8000
```

## 📱 功能介绍

网站包含以下主要部分：

1. **导航栏**：固定顶部导航，毛玻璃效果
2. **英雄区域**：主标题和小程序简介
3. **功能介绍**：6个核心功能的详细说明
4. **二维码区域**：小程序二维码和使用说明（支持点击放大）
5. **扩展功能**：液量核算系统和赞助支持链接
6. **底部信息**：联系方式和版权信息

## 🎯 核心功能展示

- 🧪 **含油化验计算**：专业的含油化验相关计算工具
- ⏰ **倒班时间查询**：智能倒班时间查询系统
- 📊 **药剂标定统计**：药剂标定数据统计分析
- 🔍 **仪表位号查询**：快速查询仪表及位号信息
- ⚠️ **防爆标识查询**：防爆标识快速查询识别
- 📸 **证件照背景更换**：便捷的证件照背景更换工具

## 🔗 扩展功能链接

### 🛢️ 海四液量核算系统
- **网址**：https://tj.sunxiyue.com/
- **功能**：专业的油井液量核算管理系统
- **特色**：Excel数据导入、停开井状态管理、产量影响计算、支线统计分析

### ❤️ 友情赞助
- **网址**：https://sunxiyue.com/2/
- **说明**：支持开发者继续开发更多实用工具
- **方式**：自愿赞助，用于工具维护和功能改进

## 🔄 添加小程序二维码

要添加真实的小程序二维码：

1. **获取二维码**：在微信公众平台获取小程序二维码
2. **保存图片**：将二维码图片重命名为 `qr-code.png`
3. **上传文件**：将图片上传到 `wd/images/` 文件夹
4. **自动显示**：网页会自动加载并显示二维码

### 📱 二维码要求：
- **文件名**：必须为 `qr-code.png`
- **位置**：`wd/images/qr-code.png`
- **尺寸**：建议 400x400 像素或更高
- **格式**：PNG格式（支持透明背景）

### 🔧 故障处理：
如果二维码图片不存在或加载失败，页面会自动显示占位符和提示信息。

## 🎨 自定义样式

可以通过修改CSS变量来自定义主题色彩：

```css
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-background: rgba(255, 255, 255, 0.95);
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
}
```

## 📞 联系信息

- **邮箱**：<EMAIL>
- **官网**：https://www.sunxiyue.com
- **小程序**：海上平台常用查询工具

## 📄 许可证

© 2024 海上平台常用查询工具. All rights reserved.
