<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中药处方对比分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .tab.active {
            background: #3498db;
            color: white;
        }
        
        .tab:hover {
            background: #ecf0f1;
        }
        
        .tab.active:hover {
            background: #2980b9;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .card h2 {
            color: #e74c3c;
            font-size: 1.8em;
            margin-bottom: 20px;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }
        
        .prescription-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .prescription-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .prescription-1 {
            border-left: 5px solid #e74c3c;
        }
        
        .prescription-2 {
            border-left: 5px solid #3498db;
        }
        
        .medicine-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .medicine-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .medicine-item:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }
        
        .medicine-name {
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        
        .medicine-dose {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .compare-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .compare-table th,
        .compare-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .compare-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .compare-table tr:hover {
            background: #f5f5f5;
        }
        
        .diff-highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .same-highlight {
            background: #d4edda;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        @media (max-width: 768px) {
            .prescription-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .medicine-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 中药处方对比分析</h1>
            <p>两个处方的详细解读与对比研究</p>
        </div>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('prescription1')">📋 处方一详解</div>
            <div class="tab" onclick="showTab('prescription2')">📋 处方二详解</div>
            <div class="tab" onclick="showTab('compare')">⚖️ 对比分析</div>
            <div class="tab" onclick="showTab('clinical')">🏥 临床应用</div>
        </div>
        
        <!-- 处方一内容 -->
        <div id="prescription1" class="tab-content active">
            <div class="card">
                <h2>📋 处方一组成 (R 约味:23)</h2>
                <div class="medicine-list">
                    <div class="medicine-item">
                        <div class="medicine-name">白芍</div>
                        <div class="medicine-dose">10g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">炒桃仁</div>
                        <div class="medicine-dose">18g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">赤小豆</div>
                        <div class="medicine-dose">120g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">大枣</div>
                        <div class="medicine-dose">112g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">紫苏黄芩</div>
                        <div class="medicine-dose">115g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">红花</div>
                        <div class="medicine-dose">4g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">黑脂麻片</div>
                        <div class="medicine-dose">10g × 先煎30分钟</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">酒黄芩</div>
                        <div class="medicine-dose">115g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">清半夏</div>
                        <div class="medicine-dose">10g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">人参片</div>
                        <div class="medicine-dose">20g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">盐菟丝子</div>
                        <div class="medicine-dose">112g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">炙甘草</div>
                        <div class="medicine-dose">10g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">陈皮</div>
                        <div class="medicine-dose">19g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">川芎</div>
                        <div class="medicine-dose">6g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">当归</div>
                        <div class="medicine-dose">10g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">茯苓白术</div>
                        <div class="medicine-dose">128g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">干姜</div>
                        <div class="medicine-dose">20g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">黄芪</div>
                        <div class="medicine-dose">130g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">枸杞子</div>
                        <div class="medicine-dose">115g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">牡蛎</div>
                        <div class="medicine-dose">130g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">肉桂</div>
                        <div class="medicine-dose">15g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">山药</div>
                        <div class="medicine-dose">124g × 1000g/袋</div>
                    </div>
                    <div class="medicine-item">
                        <div class="medicine-name">薏苡仁</div>
                        <div class="medicine-dose">120g × 1000g/袋</div>
                    </div>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-top: 30px;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">📊 处方一统计信息</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div><strong>单剂用量：</strong>343g</div>
                        <div><strong>总用量：</strong>1715g</div>
                        <div><strong>共：</strong>5剂</div>
                        <div><strong>每剂：</strong>2包</div>
                        <div><strong>每包：</strong>200ml/次</div>
                        <div><strong>煎药次数：</strong>二煎</div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            function showTab(tabName) {
                // 隐藏所有标签内容
                const contents = document.querySelectorAll('.tab-content');
                contents.forEach(content => content.classList.remove('active'));
                
                // 移除所有标签的active类
                const tabs = document.querySelectorAll('.tab');
                tabs.forEach(tab => tab.classList.remove('active'));
                
                // 显示选中的标签内容
                document.getElementById(tabName).classList.add('active');
                
                // 添加active类到选中的标签
                event.target.classList.add('active');
            }
        </script>
    </div>
</body>
</html>
